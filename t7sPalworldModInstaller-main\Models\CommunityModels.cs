using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace ModInstallerApp.Models
{
    /// <summary>
    /// Represents a shareable mod collection
    /// </summary>
    public class ModCollection
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public string Author { get; set; } = "";
        public string Version { get; set; } = "1.0.0";
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime ModifiedDate { get; set; } = DateTime.Now;
        public List<ModCollectionItem> Mods { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
        public List<string> Tags { get; set; } = new();
        public ModCollectionType Type { get; set; } = ModCollectionType.Custom;
        public string GameVersion { get; set; } = "";
        public bool RequiresUE4SS { get; set; }
        public bool RequiresPalSchema { get; set; }
        public List<string> Dependencies { get; set; } = new();
        public ModCollectionRating Rating { get; set; } = new();
        public string ExportPath { get; set; } = "";
        public long TotalSize { get; set; }
        public int InstallCount { get; set; }
    }

    /// <summary>
    /// Represents a mod within a collection
    /// </summary>
    public class ModCollectionItem
    {
        public string ModId { get; set; } = "";
        public string Name { get; set; } = "";
        public string Version { get; set; } = "";
        public string Author { get; set; } = "";
        public string SourcePath { get; set; } = "";
        public string ArchiveName { get; set; } = "";
        public ModType ModType { get; set; } = ModType.Unknown;
        public bool IsEnabled { get; set; } = true;
        public int LoadOrder { get; set; }
        public Dictionary<string, object> Configuration { get; set; } = new();
        public List<string> Dependencies { get; set; } = new();
        public string Hash { get; set; } = "";
        public long Size { get; set; }
        public DateTime LastModified { get; set; } = DateTime.Now;
        public bool IsRequired { get; set; }
        public string Notes { get; set; } = "";
    }

    /// <summary>
    /// Rating and review information for collections
    /// </summary>
    public class ModCollectionRating
    {
        public double AverageRating { get; set; }
        public int TotalRatings { get; set; }
        public List<ModReview> Reviews { get; set; } = new();
        public Dictionary<int, int> RatingDistribution { get; set; } = new(); // Rating (1-5) -> Count
    }

    /// <summary>
    /// Individual mod review
    /// </summary>
    public class ModReview
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string ReviewerName { get; set; } = "";
        public int Rating { get; set; } = 5; // 1-5 stars
        public string Title { get; set; } = "";
        public string Comment { get; set; } = "";
        public DateTime ReviewDate { get; set; } = DateTime.Now;
        public bool IsVerified { get; set; }
        public List<string> Tags { get; set; } = new();
        public int HelpfulVotes { get; set; }
        public int TotalVotes { get; set; }
    }

    /// <summary>
    /// Export/Import package for mod collections
    /// </summary>
    public class ModCollectionPackage
    {
        public string FormatVersion { get; set; } = "1.0";
        public ModCollection Collection { get; set; } = new();
        public List<ModProfile> Profiles { get; set; } = new();
        public List<PalSchemaConfig> PalSchemaConfigs { get; set; } = new();
        public Dictionary<string, byte[]> ModArchives { get; set; } = new(); // ModId -> Archive data
        public ExportMetadata ExportInfo { get; set; } = new();
        public List<string> RequiredDependencies { get; set; } = new();
        public string Checksum { get; set; } = "";
    }

    /// <summary>
    /// Metadata about the export
    /// </summary>
    public class ExportMetadata
    {
        public string ExportedBy { get; set; } = Environment.UserName;
        public DateTime ExportDate { get; set; } = DateTime.Now;
        public string ExporterVersion { get; set; } = "1.4.0";
        public string GameVersion { get; set; } = "";
        public string UE4SSVersion { get; set; } = "";
        public string PalSchemaVersion { get; set; } = "";
        public Dictionary<string, string> SystemInfo { get; set; } = new();
        public bool IncludeModFiles { get; set; } = true;
        public bool IncludeConfigurations { get; set; } = true;
        public bool IncludeProfiles { get; set; } = true;
    }

    /// <summary>
    /// Diagnostic report for troubleshooting
    /// </summary>
    public class DiagnosticReport
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public DateTime GeneratedAt { get; set; } = DateTime.Now;
        public string GeneratedBy { get; set; } = Environment.UserName;
        public SystemInformation SystemInfo { get; set; } = new();
        public GameInformation GameInfo { get; set; } = new();
        public List<ModDiagnosticInfo> ModDiagnostics { get; set; } = new();
        public List<DiagnosticIssue> DetectedIssues { get; set; } = new();
        public List<string> LogEntries { get; set; } = new();
        public Dictionary<string, object> PerformanceMetrics { get; set; } = new();
        public List<string> RecommendedActions { get; set; } = new();
    }

    /// <summary>
    /// System information for diagnostics
    /// </summary>
    public class SystemInformation
    {
        public string OperatingSystem { get; set; } = "";
        public string ProcessorArchitecture { get; set; } = "";
        public long TotalMemory { get; set; }
        public long AvailableMemory { get; set; }
        public string DotNetVersion { get; set; } = "";
        public List<string> InstalledFrameworks { get; set; } = new();
        public Dictionary<string, string> EnvironmentVariables { get; set; } = new();
        public string UserName { get; set; } = "";
        public bool IsAdministrator { get; set; }
    }

    /// <summary>
    /// Game-specific information for diagnostics
    /// </summary>
    public class GameInformation
    {
        public string GamePath { get; set; } = "";
        public string GameVersion { get; set; } = "";
        public bool IsGameRunning { get; set; }
        public UE4SSStatus UE4SSStatus { get; set; } = new();
        public PalSchemaStatus PalSchemaStatus { get; set; } = new();
        public List<string> InstalledMods { get; set; } = new();
        public List<string> EnabledMods { get; set; } = new();
        public Dictionary<string, string> GameSettings { get; set; } = new();
        public long GameDirectorySize { get; set; }
        public DateTime LastGameLaunch { get; set; }
    }

    /// <summary>
    /// Diagnostic information for individual mods
    /// </summary>
    public class ModDiagnosticInfo
    {
        public string ModId { get; set; } = "";
        public string Name { get; set; } = "";
        public ModType Type { get; set; } = ModType.Unknown;
        public bool IsEnabled { get; set; }
        public bool IsValid { get; set; }
        public List<string> Issues { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public Dictionary<string, object> Properties { get; set; } = new();
        public List<string> Dependencies { get; set; } = new();
        public List<string> Conflicts { get; set; } = new();
        public long Size { get; set; }
        public DateTime LastModified { get; set; }
    }

    /// <summary>
    /// Represents a detected issue in the diagnostic report
    /// </summary>
    public class DiagnosticIssue
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public DiagnosticSeverity Severity { get; set; } = DiagnosticSeverity.Info;
        public string Category { get; set; } = "";
        public List<string> AffectedMods { get; set; } = new();
        public List<string> RecommendedActions { get; set; } = new();
        public bool CanAutoFix { get; set; }
        public string AutoFixDescription { get; set; } = "";
        public Dictionary<string, object> Details { get; set; } = new();
    }

    /// <summary>
    /// Common issues that can be automatically detected and fixed
    /// </summary>
    public class CommonIssue
    {
        public string Id { get; set; } = "";
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public Func<bool> DetectionMethod { get; set; } = () => false;
        public Func<bool> FixMethod { get; set; } = () => false;
        public DiagnosticSeverity Severity { get; set; } = DiagnosticSeverity.Warning;
        public List<string> Categories { get; set; } = new();
        public bool RequiresUserConfirmation { get; set; } = true;
    }

    // Enums
    public enum ModCollectionType
    {
        Custom,
        Curated,
        Community,
        Official,
        Backup
    }

    public enum DiagnosticSeverity
    {
        Info,
        Warning,
        Error,
        Critical
    }

    public enum ExportFormat
    {
        Json,
        Binary,
        Archive
    }

    public enum ImportResult
    {
        Success,
        PartialSuccess,
        Failed,
        Cancelled
    }
}
