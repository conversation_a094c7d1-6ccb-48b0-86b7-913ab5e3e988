using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using ModInstallerApp.Models;

namespace ModInstallerApp.Services
{
    /// <summary>
    /// Manager for PalSchema profiles with comparison, diffing, and template functionality
    /// </summary>
    public class PalSchemaProfileManager : IDisposable
    {
        private readonly PalSchemaConfigurationService _configService;
        private readonly EnhancedLogger _logger;
        private readonly string _profilesPath;
        private readonly string _backupsPath;
        private bool _disposed = false;

        private readonly List<PalSchemaProfile> _profiles = new();
        private PalSchemaProfile? _activeProfile;

        public event EventHandler<PalSchemaProfile>? ProfileActivated;
        public event EventHandler<PalSchemaProfile>? ProfileCreated;
        public event EventHandler<PalSchemaProfile>? ProfileUpdated;
        public event EventHandler<PalSchemaProfile>? ProfileDeleted;

        public PalSchemaProfile? ActiveProfile => _activeProfile;
        public IReadOnlyList<PalSchemaProfile> Profiles => _profiles.AsReadOnly();

        public PalSchemaProfileManager(PalSchemaConfigurationService configService, EnhancedLogger logger)
        {
            _configService = configService ?? throw new ArgumentNullException(nameof(configService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            var appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "PalworldModInstaller");
            _profilesPath = Path.Combine(appDataPath, "PalSchemaProfiles");
            _backupsPath = Path.Combine(appDataPath, "PalSchemaBackups");

            Directory.CreateDirectory(_profilesPath);
            Directory.CreateDirectory(_backupsPath);
        }

        /// <summary>
        /// Loads all profiles from disk
        /// </summary>
        public async Task LoadProfilesAsync()
        {
            ThrowIfDisposed();

            try
            {
                _profiles.Clear();
                var profileFiles = Directory.GetFiles(_profilesPath, "*.json");

                foreach (var file in profileFiles)
                {
                    try
                    {
                        var json = await File.ReadAllTextAsync(file);
                        var profile = JsonSerializer.Deserialize<PalSchemaProfile>(json);
                        if (profile != null)
                        {
                            _profiles.Add(profile);
                            if (profile.IsActive)
                            {
                                _activeProfile = profile;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"Failed to load profile from {file}", "PalSchemaProfile", ex);
                    }
                }

                _logger.LogInfo($"Loaded {_profiles.Count} PalSchema profiles", "PalSchemaProfile");
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to load PalSchema profiles", "PalSchemaProfile", ex);
                throw;
            }
        }

        /// <summary>
        /// Creates a new profile
        /// </summary>
        public async Task<PalSchemaProfile> CreateProfileAsync(string name, string description = "")
        {
            ThrowIfDisposed();

            var profile = new PalSchemaProfile
            {
                Name = name,
                Description = description,
                CreatedDate = DateTime.Now,
                ModifiedDate = DateTime.Now
            };

            await SaveProfileAsync(profile);
            _profiles.Add(profile);

            ProfileCreated?.Invoke(this, profile);
            _logger.LogInfo($"Created PalSchema profile: {name}", "PalSchemaProfile");

            return profile;
        }

        /// <summary>
        /// Saves a profile to disk
        /// </summary>
        public async Task SaveProfileAsync(PalSchemaProfile profile)
        {
            ThrowIfDisposed();

            try
            {
                profile.ModifiedDate = DateTime.Now;

                var fileName = $"{SanitizeFileName(profile.Name)}_{profile.Id}.json";
                var filePath = Path.Combine(_profilesPath, fileName);

                var options = new JsonSerializerOptions { WriteIndented = true };
                var json = JsonSerializer.Serialize(profile, options);

                await File.WriteAllTextAsync(filePath, json);

                // Update in-memory collection
                var existing = _profiles.FirstOrDefault(p => p.Id == profile.Id);
                if (existing != null)
                {
                    var index = _profiles.IndexOf(existing);
                    _profiles[index] = profile;
                }

                ProfileUpdated?.Invoke(this, profile);
                _logger.LogInfo($"Saved PalSchema profile: {profile.Name}", "PalSchemaProfile");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to save profile: {profile.Name}", "PalSchemaProfile", ex);
                throw;
            }
        }

        /// <summary>
        /// Activates a profile
        /// </summary>
        public async Task ActivateProfileAsync(PalSchemaProfile profile)
        {
            ThrowIfDisposed();

            try
            {
                // Create backup of current active profile if exists
                if (_activeProfile != null)
                {
                    await CreateProfileBackupAsync(_activeProfile, "Profile deactivation backup");
                    _activeProfile.IsActive = false;
                    await SaveProfileAsync(_activeProfile);
                }

                // Activate new profile
                profile.IsActive = true;
                await SaveProfileAsync(profile);
                _activeProfile = profile;

                ProfileActivated?.Invoke(this, profile);
                _logger.LogInfo($"Activated PalSchema profile: {profile.Name}", "PalSchemaProfile");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to activate profile: {profile.Name}", "PalSchemaProfile", ex);
                throw;
            }
        }

        /// <summary>
        /// Deletes a profile
        /// </summary>
        public async Task DeleteProfileAsync(PalSchemaProfile profile)
        {
            ThrowIfDisposed();

            try
            {
                // Create backup before deletion
                await CreateProfileBackupAsync(profile, "Profile deletion backup");

                // Remove from disk
                var fileName = $"{SanitizeFileName(profile.Name)}_{profile.Id}.json";
                var filePath = Path.Combine(_profilesPath, fileName);
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }

                // Remove from memory
                _profiles.Remove(profile);

                // If this was the active profile, clear it
                if (_activeProfile?.Id == profile.Id)
                {
                    _activeProfile = null;
                }

                ProfileDeleted?.Invoke(this, profile);
                _logger.LogInfo($"Deleted PalSchema profile: {profile.Name}", "PalSchemaProfile");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to delete profile: {profile.Name}", "PalSchemaProfile", ex);
                throw;
            }
        }

        /// <summary>
        /// Compares two profiles and returns differences
        /// </summary>
        public List<PalSchemaConfigComparison> CompareProfiles(PalSchemaProfile profile1, PalSchemaProfile profile2)
        {
            ThrowIfDisposed();

            var comparisons = new List<PalSchemaConfigComparison>();

            try
            {
                // Compare basic properties
                if (profile1.Name != profile2.Name)
                {
                    comparisons.Add(new PalSchemaConfigComparison
                    {
                        PropertyPath = "Name",
                        LeftValue = profile1.Name,
                        RightValue = profile2.Name,
                        ComparisonType = PalSchemaComparisonType.Modified,
                        Description = "Profile name differs"
                    });
                }

                if (profile1.Description != profile2.Description)
                {
                    comparisons.Add(new PalSchemaConfigComparison
                    {
                        PropertyPath = "Description",
                        LeftValue = profile1.Description,
                        RightValue = profile2.Description,
                        ComparisonType = PalSchemaComparisonType.Modified,
                        Description = "Profile description differs"
                    });
                }

                // Compare configurations
                var config1Dict = profile1.Configurations.ToDictionary(c => c.Id, c => c);
                var config2Dict = profile2.Configurations.ToDictionary(c => c.Id, c => c);

                // Find added configurations
                foreach (var config in profile2.Configurations.Where(c => !config1Dict.ContainsKey(c.Id)))
                {
                    comparisons.Add(new PalSchemaConfigComparison
                    {
                        PropertyPath = $"Configurations.{config.Name}",
                        LeftValue = null,
                        RightValue = config.Name,
                        ComparisonType = PalSchemaComparisonType.Added,
                        Description = $"Configuration '{config.Name}' was added"
                    });
                }

                // Find removed configurations
                foreach (var config in profile1.Configurations.Where(c => !config2Dict.ContainsKey(c.Id)))
                {
                    comparisons.Add(new PalSchemaConfigComparison
                    {
                        PropertyPath = $"Configurations.{config.Name}",
                        LeftValue = config.Name,
                        RightValue = null,
                        ComparisonType = PalSchemaComparisonType.Removed,
                        Description = $"Configuration '{config.Name}' was removed"
                    });
                }

                // Find modified configurations
                foreach (var config1 in profile1.Configurations.Where(c => config2Dict.ContainsKey(c.Id)))
                {
                    var config2 = config2Dict[config1.Id];
                    var configComparisons = CompareConfigurations(config1, config2);
                    comparisons.AddRange(configComparisons);
                }

                _logger.LogInfo($"Compared profiles: {profile1.Name} vs {profile2.Name} ({comparisons.Count} differences)", "PalSchemaProfile");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to compare profiles: {profile1.Name} vs {profile2.Name}", "PalSchemaProfile", ex);
                throw;
            }

            return comparisons;
        }

        /// <summary>
        /// Compares two configurations and returns differences
        /// </summary>
        public List<PalSchemaConfigComparison> CompareConfigurations(PalSchemaConfig config1, PalSchemaConfig config2)
        {
            var comparisons = new List<PalSchemaConfigComparison>();

            try
            {
                // Compare basic properties
                if (config1.Name != config2.Name)
                {
                    comparisons.Add(new PalSchemaConfigComparison
                    {
                        PropertyPath = $"Configurations.{config1.Id}.Name",
                        LeftValue = config1.Name,
                        RightValue = config2.Name,
                        ComparisonType = PalSchemaComparisonType.Modified,
                        Description = "Configuration name differs"
                    });
                }

                // Compare configuration dictionaries
                CompareConfigurationDictionaries(config1.Configuration, config2.Configuration, 
                    $"Configurations.{config1.Name}", comparisons);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to compare configurations: {config1.Name} vs {config2.Name}", "PalSchemaProfile", ex);
                throw;
            }

            return comparisons;
        }

        /// <summary>
        /// Creates a profile from a template
        /// </summary>
        public async Task<PalSchemaProfile> CreateProfileFromTemplateAsync(string profileName, PalSchemaTemplate template, Dictionary<string, object>? parameterValues = null)
        {
            ThrowIfDisposed();

            var profile = new PalSchemaProfile
            {
                Name = profileName,
                Description = $"Profile created from template: {template.Name}",
                CreatedDate = DateTime.Now,
                ModifiedDate = DateTime.Now
            };

            // Create configuration from template
            var config = _configService.CreateFromTemplate(template, parameterValues);
            config.Name = $"{template.Name} Configuration";
            profile.Configurations.Add(config);

            await SaveProfileAsync(profile);
            _profiles.Add(profile);

            ProfileCreated?.Invoke(this, profile);
            _logger.LogInfo($"Created profile from template: {profileName} (template: {template.Name})", "PalSchemaProfile");

            return profile;
        }

        /// <summary>
        /// Creates a backup of a profile
        /// </summary>
        public async Task<PalSchemaBackupMetadata> CreateProfileBackupAsync(PalSchemaProfile profile, string reason = "Manual backup")
        {
            ThrowIfDisposed();

            try
            {
                var backup = new PalSchemaBackupMetadata
                {
                    BackupDate = DateTime.Now,
                    BackupReason = reason,
                    ProfileId = profile.Id,
                    IsAutoBackup = reason.Contains("Auto") || reason.Contains("automatic")
                };

                var backupFileName = $"profile_backup_{profile.Id}_{DateTime.Now:yyyyMMdd_HHmmss}.json";
                backup.BackupPath = Path.Combine(_backupsPath, backupFileName);

                var options = new JsonSerializerOptions { WriteIndented = true };
                var json = JsonSerializer.Serialize(profile, options);
                await File.WriteAllTextAsync(backup.BackupPath, json);

                var fileInfo = new FileInfo(backup.BackupPath);
                backup.BackupSize = fileInfo.Length;
                backup.ChecksumHash = CalculateChecksum(json);

                // Save backup metadata
                var metadataPath = Path.ChangeExtension(backup.BackupPath, ".metadata.json");
                var metadataJson = JsonSerializer.Serialize(backup, options);
                await File.WriteAllTextAsync(metadataPath, metadataJson);

                _logger.LogInfo($"Created backup for profile: {profile.Name} (reason: {reason})", "PalSchemaProfile");
                return backup;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to create backup for profile: {profile.Name}", "PalSchemaProfile", ex);
                throw;
            }
        }

        /// <summary>
        /// Validates a profile and all its configurations
        /// </summary>
        public async Task<PalSchemaProfileValidation> ValidateProfileAsync(PalSchemaProfile profile)
        {
            ThrowIfDisposed();

            var validation = new PalSchemaProfileValidation
            {
                LastValidated = DateTime.Now
            };

            try
            {
                // Validate each configuration in the profile
                foreach (var config in profile.Configurations)
                {
                    var configResults = await _configService.ValidateConfigurationAsync(config);
                    validation.ValidationResults.AddRange(configResults);
                }

                // Check for conflicting configurations
                DetectConfigurationConflicts(profile, validation);

                // Check for missing dependencies
                await CheckMissingDependencies(profile, validation);

                validation.IsValid = !validation.ValidationResults.Any(r => r.Severity >= PalSchemaValidationSeverity.Error);
                profile.ValidationStatus = validation;

                _logger.LogInfo($"Validated profile: {profile.Name} (Valid: {validation.IsValid})", "PalSchemaProfile");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to validate profile: {profile.Name}", "PalSchemaProfile", ex);
                validation.IsValid = false;
                validation.ValidationResults.Add(new PalSchemaValidationResult
                {
                    PropertyPath = "General",
                    Severity = PalSchemaValidationSeverity.Critical,
                    Message = $"Profile validation failed: {ex.Message}"
                });
            }

            return validation;
        }

        private void CompareConfigurationDictionaries(Dictionary<string, object> dict1, Dictionary<string, object> dict2, 
            string basePath, List<PalSchemaConfigComparison> comparisons)
        {
            // Find added keys
            foreach (var key in dict2.Keys.Where(k => !dict1.ContainsKey(k)))
            {
                comparisons.Add(new PalSchemaConfigComparison
                {
                    PropertyPath = $"{basePath}.{key}",
                    LeftValue = null,
                    RightValue = dict2[key],
                    ComparisonType = PalSchemaComparisonType.Added,
                    Description = $"Property '{key}' was added"
                });
            }

            // Find removed keys
            foreach (var key in dict1.Keys.Where(k => !dict2.ContainsKey(k)))
            {
                comparisons.Add(new PalSchemaConfigComparison
                {
                    PropertyPath = $"{basePath}.{key}",
                    LeftValue = dict1[key],
                    RightValue = null,
                    ComparisonType = PalSchemaComparisonType.Removed,
                    Description = $"Property '{key}' was removed"
                });
            }

            // Find modified keys
            foreach (var key in dict1.Keys.Where(k => dict2.ContainsKey(k)))
            {
                var value1 = dict1[key];
                var value2 = dict2[key];

                if (!AreValuesEqual(value1, value2))
                {
                    comparisons.Add(new PalSchemaConfigComparison
                    {
                        PropertyPath = $"{basePath}.{key}",
                        LeftValue = value1,
                        RightValue = value2,
                        ComparisonType = PalSchemaComparisonType.Modified,
                        Description = $"Property '{key}' value changed"
                    });
                }
            }
        }

        private bool AreValuesEqual(object? value1, object? value2)
        {
            if (value1 == null && value2 == null) return true;
            if (value1 == null || value2 == null) return false;

            // For complex objects, serialize and compare
            if (value1 is Dictionary<string, object> || value2 is Dictionary<string, object>)
            {
                var json1 = JsonSerializer.Serialize(value1);
                var json2 = JsonSerializer.Serialize(value2);
                return json1 == json2;
            }

            return value1.Equals(value2);
        }

        private void DetectConfigurationConflicts(PalSchemaProfile profile, PalSchemaProfileValidation validation)
        {
            // Simple conflict detection - check for configurations with same names
            var configNames = profile.Configurations.GroupBy(c => c.Name)
                .Where(g => g.Count() > 1)
                .Select(g => g.Key);

            foreach (var name in configNames)
            {
                validation.ConflictingConfigurations.Add(name);
                validation.ValidationResults.Add(new PalSchemaValidationResult
                {
                    PropertyPath = $"Configurations.{name}",
                    Severity = PalSchemaValidationSeverity.Warning,
                    Message = $"Multiple configurations with the same name: {name}",
                    SuggestedFix = "Rename one of the conflicting configurations"
                });
            }
        }

        private async Task CheckMissingDependencies(PalSchemaProfile profile, PalSchemaProfileValidation validation)
        {
            // TODO: Implement dependency checking logic
            await Task.CompletedTask;
        }

        private string CalculateChecksum(string content)
        {
            using var sha256 = System.Security.Cryptography.SHA256.Create();
            var hash = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(content));
            return Convert.ToBase64String(hash);
        }

        private string SanitizeFileName(string fileName)
        {
            var invalidChars = Path.GetInvalidFileNameChars();
            return string.Join("_", fileName.Split(invalidChars, StringSplitOptions.RemoveEmptyEntries));
        }

        private void ThrowIfDisposed()
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(PalSchemaProfileManager));
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _disposed = true;
            }
        }
    }
}
