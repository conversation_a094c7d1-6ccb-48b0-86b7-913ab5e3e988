using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using ModInstallerApp.Models;

namespace ModInstallerApp.Services
{
    /// <summary>
    /// Service for managing mod collections, profiles, and state
    /// </summary>
    public class ModManagerService : IDisposable
    {
        private readonly string _palRoot;
        private readonly EnhancedInstallationEngine _installationEngine;
        private readonly EnhancedLogger _logger;
        private readonly ModDependencyService _dependencyService;
        private readonly List<ModItem> _mods = new();
        private readonly List<ModProfile> _profiles = new();
        private readonly List<ModCollection> _collections = new();
        private ModProfile? _activeProfile;
        private bool _disposed;

        public event EventHandler<ModItem>? ModStateChanged;
        public event EventHandler<ModProfile>? ProfileChanged;
        public event EventHandler<List<ModItem>>? ModsUpdated;

        public IReadOnlyList<ModItem> Mods => _mods.AsReadOnly();
        public IReadOnlyList<ModProfile> Profiles => _profiles.AsReadOnly();
        public IReadOnlyList<ModCollection> Collections => _collections.AsReadOnly();
        public ModProfile? ActiveProfile => _activeProfile;

        // Additional methods for UI compatibility
        public List<ModItem> GetAllMods() => _mods.ToList();
        public List<ModProfile> GetAllProfiles() => _profiles.ToList();

        public ModManagerService(string palRoot, EnhancedInstallationEngine installationEngine, EnhancedLogger logger)
        {
            _palRoot = palRoot ?? throw new ArgumentNullException(nameof(palRoot));
            _installationEngine = installationEngine ?? throw new ArgumentNullException(nameof(installationEngine));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _dependencyService = new ModDependencyService(logger);
        }

        public async Task InitializeAsync()
        {
            try
            {
                _logger.LogInfo("Initializing Mod Manager Service", "ModManager");
                
                await LoadModsAsync();
                await LoadProfilesAsync();
                await LoadCollectionsAsync();
                
                _logger.LogInfo($"Mod Manager initialized: {_mods.Count} mods, {_profiles.Count} profiles, {_collections.Count} collections", "ModManager");
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to initialize Mod Manager Service", "ModManager", ex);
                throw;
            }
        }

        public async Task LoadModsAsync()
        {
            try
            {
                _mods.Clear();
                
                // Load from installation records
                var installations = await _installationEngine.GetAllInstallationsAsync();
                
                foreach (var installation in installations)
                {
                    var modItem = CreateModItemFromInstallation(installation);
                    _mods.Add(modItem);
                }

                // Sort by load order, then by name
                _mods.Sort((a, b) =>
                {
                    var orderComparison = a.LoadOrder.CompareTo(b.LoadOrder);
                    return orderComparison != 0 ? orderComparison : string.Compare(a.Name, b.Name, StringComparison.OrdinalIgnoreCase);
                });

                ModsUpdated?.Invoke(this, _mods);
                _logger.LogInfo($"Loaded {_mods.Count} mods", "ModManager");
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to load mods", "ModManager", ex);
                throw;
            }
        }

        public async Task<bool> SetModEnabledAsync(string modId, bool enabled)
        {
            try
            {
                var mod = _mods.FirstOrDefault(m => m.Id == modId);
                if (mod == null)
                {
                    _logger.LogWarning($"Mod not found: {modId}", "ModManager");
                    return false;
                }

                if (mod.IsEnabled == enabled)
                    return true; // No change needed

                mod.IsEnabled = enabled;
                mod.State = enabled ? ModState.Installed : ModState.Disabled;
                
                // Apply the change to the file system
                await ApplyModStateAsync(mod);
                
                // Update active profile if exists
                if (_activeProfile != null)
                {
                    var profileEntry = _activeProfile.Mods.FirstOrDefault(m => m.ModId == modId);
                    if (profileEntry != null)
                    {
                        profileEntry.IsEnabled = enabled;
                        await SaveProfileAsync(_activeProfile);
                    }
                }

                ModStateChanged?.Invoke(this, mod);
                _logger.LogInfo($"Mod {mod.Name} {(enabled ? "enabled" : "disabled")}", "ModManager");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to set mod enabled state: {modId}", "ModManager", ex);
                return false;
            }
        }

        public async Task<bool> SetModLoadOrderAsync(string modId, int loadOrder)
        {
            try
            {
                var mod = _mods.FirstOrDefault(m => m.Id == modId);
                if (mod == null)
                    return false;

                mod.LoadOrder = loadOrder;
                
                // Re-sort mods
                _mods.Sort((a, b) =>
                {
                    var orderComparison = a.LoadOrder.CompareTo(b.LoadOrder);
                    return orderComparison != 0 ? orderComparison : string.Compare(a.Name, b.Name, StringComparison.OrdinalIgnoreCase);
                });

                // Update active profile
                if (_activeProfile != null)
                {
                    var profileEntry = _activeProfile.Mods.FirstOrDefault(m => m.ModId == modId);
                    if (profileEntry != null)
                    {
                        profileEntry.LoadOrder = loadOrder;
                        await SaveProfileAsync(_activeProfile);
                    }
                }

                ModsUpdated?.Invoke(this, _mods);
                _logger.LogInfo($"Mod {mod.Name} load order set to {loadOrder}", "ModManager");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to set mod load order: {modId}", "ModManager", ex);
                return false;
            }
        }

        public async Task<ModProfile> CreateProfileAsync(string name, string description = "")
        {
            var profile = new ModProfile
            {
                Name = name,
                Description = description,
                Created = DateTime.Now,
                LastModified = DateTime.Now
            };

            // Copy current mod states
            foreach (var mod in _mods)
            {
                profile.Mods.Add(new ModProfileEntry
                {
                    ModId = mod.Id,
                    IsEnabled = mod.IsEnabled,
                    LoadOrder = mod.LoadOrder
                });
            }

            _profiles.Add(profile);
            await SaveProfileAsync(profile);
            
            _logger.LogInfo($"Created profile: {name}", "ModManager");
            return profile;
        }

        public async Task<bool> LoadProfileAsync(string profileId)
        {
            try
            {
                var profile = _profiles.FirstOrDefault(p => p.Id == profileId);
                if (profile == null)
                    return false;

                // Apply profile settings to mods
                foreach (var entry in profile.Mods)
                {
                    var mod = _mods.FirstOrDefault(m => m.Id == entry.ModId);
                    if (mod != null)
                    {
                        mod.IsEnabled = entry.IsEnabled;
                        mod.LoadOrder = entry.LoadOrder;
                        mod.State = entry.IsEnabled ? ModState.Installed : ModState.Disabled;
                        
                        await ApplyModStateAsync(mod);
                    }
                }

                // Update active profile
                if (_activeProfile != null)
                    _activeProfile.IsActive = false;
                
                _activeProfile = profile;
                profile.IsActive = true;

                // Re-sort mods
                _mods.Sort((a, b) =>
                {
                    var orderComparison = a.LoadOrder.CompareTo(b.LoadOrder);
                    return orderComparison != 0 ? orderComparison : string.Compare(a.Name, b.Name, StringComparison.OrdinalIgnoreCase);
                });

                ModsUpdated?.Invoke(this, _mods);
                ProfileChanged?.Invoke(this, profile);
                
                _logger.LogInfo($"Loaded profile: {profile.Name}", "ModManager");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to load profile: {profileId}", "ModManager", ex);
                return false;
            }
        }

        public List<ModItem> FilterMods(ModFilter filter)
        {
            var query = _mods.AsEnumerable();

            // Text search
            if (!string.IsNullOrWhiteSpace(filter.SearchText))
            {
                var searchText = filter.SearchText.ToLowerInvariant();
                query = query.Where(m =>
                    m.Name.ToLowerInvariant().Contains(searchText) ||
                    m.Description.ToLowerInvariant().Contains(searchText) ||
                    m.Author.ToLowerInvariant().Contains(searchText) ||
                    m.Tags.Any(t => t.ToLowerInvariant().Contains(searchText))
                );
            }

            // Category filter
            if (filter.Categories.Count > 0)
            {
                query = query.Where(m => filter.Categories.Contains(m.Category));
            }

            // Mod type filter
            if (filter.ModTypes.Count > 0)
            {
                query = query.Where(m => filter.ModTypes.Contains(m.ModType));
            }

            // State filter
            if (filter.States.Count > 0)
            {
                query = query.Where(m => filter.States.Contains(m.State));
            }

            // Tag filter
            if (filter.Tags.Count > 0)
            {
                query = query.Where(m => filter.Tags.Any(tag => m.Tags.Contains(tag)));
            }

            // Date filters
            if (filter.InstalledAfter.HasValue)
            {
                query = query.Where(m => m.InstallDate >= filter.InstalledAfter.Value);
            }

            if (filter.InstalledBefore.HasValue)
            {
                query = query.Where(m => m.InstallDate <= filter.InstalledBefore.Value);
            }

            // Size filters
            if (filter.MinSize.HasValue)
            {
                query = query.Where(m => m.Size >= filter.MinSize.Value);
            }

            if (filter.MaxSize.HasValue)
            {
                query = query.Where(m => m.Size <= filter.MaxSize.Value);
            }

            // Additional filters
            if (filter.ShowEnabledOnly)
            {
                query = query.Where(m => m.IsEnabled);
            }

            if (filter.ShowConflictsOnly)
            {
                query = query.Where(m => m.HasConflicts);
            }

            // Apply sorting
            query = filter.SortBy switch
            {
                ModSortCriteria.Name => filter.SortDescending ? query.OrderByDescending(m => m.Name) : query.OrderBy(m => m.Name),
                ModSortCriteria.InstallDate => filter.SortDescending ? query.OrderByDescending(m => m.InstallDate) : query.OrderBy(m => m.InstallDate),
                ModSortCriteria.LastModified => filter.SortDescending ? query.OrderByDescending(m => m.LastModified) : query.OrderBy(m => m.LastModified),
                ModSortCriteria.Size => filter.SortDescending ? query.OrderByDescending(m => m.Size) : query.OrderBy(m => m.Size),
                ModSortCriteria.Author => filter.SortDescending ? query.OrderByDescending(m => m.Author) : query.OrderBy(m => m.Author),
                ModSortCriteria.Category => filter.SortDescending ? query.OrderByDescending(m => m.Category) : query.OrderBy(m => m.Category),
                ModSortCriteria.LoadOrder => filter.SortDescending ? query.OrderByDescending(m => m.LoadOrder) : query.OrderBy(m => m.LoadOrder),
                ModSortCriteria.State => filter.SortDescending ? query.OrderByDescending(m => m.State) : query.OrderBy(m => m.State),
                _ => query
            };

            return query.ToList();
        }

        public async Task<LoadOrderValidationResult> ValidateLoadOrderAsync()
        {
            try
            {
                // Update dependencies and conflicts for all mods
                await UpdateModDependenciesAndConflictsAsync();

                // Validate load order
                var result = _dependencyService.ValidateLoadOrder(_mods);

                _logger.LogInfo($"Load order validation: {(result.IsValid ? "Valid" : "Invalid")} - {result.Errors.Count} errors, {result.Warnings.Count} warnings", "ModManager");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to validate load order", "ModManager", ex);
                return new LoadOrderValidationResult
                {
                    IsValid = false,
                    Errors = new List<string> { $"Validation failed: {ex.Message}" }
                };
            }
        }

        public async Task<List<ModItem>> SuggestOptimalLoadOrderAsync()
        {
            try
            {
                await UpdateModDependenciesAndConflictsAsync();
                var suggestedOrder = _dependencyService.SuggestLoadOrder(_mods);

                _logger.LogInfo($"Generated optimal load order for {suggestedOrder.Count} mods", "ModManager");
                return suggestedOrder;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to suggest optimal load order", "ModManager", ex);
                return _mods.Where(m => m.IsEnabled).OrderBy(m => m.LoadOrder).ToList();
            }
        }

        public async Task<bool> ApplyOptimalLoadOrderAsync()
        {
            try
            {
                var optimalOrder = await SuggestOptimalLoadOrderAsync();

                foreach (var mod in optimalOrder)
                {
                    await SetModLoadOrderAsync(mod.Id, mod.LoadOrder);
                }

                _logger.LogInfo("Applied optimal load order", "ModManager");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to apply optimal load order", "ModManager", ex);
                return false;
            }
        }

        public async Task UpdateModDependenciesAndConflictsAsync()
        {
            try
            {
                // Analyze dependencies
                var allDependencies = await _dependencyService.AnalyzeDependenciesAsync(_mods);

                // Update mod dependencies
                foreach (var mod in _mods)
                {
                    mod.Dependencies.Clear();
                    mod.Dependencies.AddRange(allDependencies.Where(d =>
                        _mods.Any(m => m.Id == d.ModId && m.Dependencies.Any(dep => dep.ModId == mod.Id))));
                }

                // Analyze conflicts
                var allConflicts = await _dependencyService.DetectConflictsAsync(_mods);

                // Update mod conflicts
                foreach (var mod in _mods)
                {
                    mod.Conflicts.Clear();
                    mod.Conflicts.AddRange(allConflicts.Where(c =>
                        allConflicts.Any(conflict => conflict.ConflictingModId == mod.Id)));
                }

                // Update mod states based on conflicts and dependencies
                foreach (var mod in _mods)
                {
                    if (mod.IsEnabled)
                    {
                        if (mod.HasConflicts && mod.Conflicts.Any(c => c.Severity >= ConflictSeverity.High))
                        {
                            mod.State = ModState.Conflicted;
                        }
                        else if (mod.Dependencies.Any(d => !d.IsSatisfied && !d.IsOptional))
                        {
                            mod.State = ModState.MissingDependencies;
                        }
                        else
                        {
                            mod.State = ModState.Installed;
                        }
                    }
                }

                _logger.LogInfo($"Updated dependencies and conflicts for {_mods.Count} mods", "ModManager");
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to update mod dependencies and conflicts", "ModManager", ex);
            }
        }

        private ModItem CreateModItemFromInstallation(InstallationRecord installation)
        {
            var modItem = new ModItem
            {
                Id = installation.Id,
                Name = installation.ModName,
                DisplayName = installation.ModName,
                Version = installation.Version,
                Author = installation.Author,
                InstallDate = installation.InstallDate,
                LastModified = installation.InstallDate,
                ModType = installation.ModType,
                Size = CalculateModSize(installation),
                InstallationPath = Path.Combine(_palRoot, "Mods", installation.ModName),
                BackupPath = installation.BackupPath ?? "",
                CanUninstall = installation.CanUninstall,
                CanRollback = installation.CanRollback,
                Metadata = installation.Metadata,
                IsEnabled = true, // Default to enabled
                State = ModState.Installed
            };

            // Determine category based on mod type and metadata
            modItem.Category = DetermineModCategory(installation);

            // Extract tags from metadata
            if (installation.Metadata.TryGetValue("tags", out var tagsObj) && tagsObj is JsonElement tagsElement)
            {
                if (tagsElement.ValueKind == JsonValueKind.Array)
                {
                    foreach (var tag in tagsElement.EnumerateArray())
                    {
                        var tagValue = tag.GetString();
                        if (!string.IsNullOrEmpty(tagValue))
                        {
                            modItem.Tags.Add(tagValue);
                        }
                    }
                }
            }

            return modItem;
        }

        private long CalculateModSize(InstallationRecord installation)
        {
            long totalSize = 0;

            foreach (var file in installation.InstalledFiles)
            {
                var fullPath = Path.Combine(_palRoot, file);
                if (File.Exists(fullPath))
                {
                    try
                    {
                        totalSize += new FileInfo(fullPath).Length;
                    }
                    catch
                    {
                        // Skip files that can't be accessed
                    }
                }
            }

            return totalSize;
        }

        private ModCategory DetermineModCategory(InstallationRecord installation)
        {
            // Check metadata first
            if (installation.Metadata.TryGetValue("category", out var categoryObj) && categoryObj is JsonElement categoryElement)
            {
                var categoryString = categoryElement.GetString();
                if (Enum.TryParse<ModCategory>(categoryString, true, out var category))
                {
                    return category;
                }
            }

            // Determine based on mod type
            return installation.ModType switch
            {
                ModType.UE4SSMod => ModCategory.Utility,
                ModType.PalSchemaMod => ModCategory.Gameplay,
                ModType.PakMod => ModCategory.Content,
                ModType.HybridMod => ModCategory.Overhaul,
                _ => ModCategory.Other
            };
        }

        private async Task ApplyModStateAsync(ModItem mod)
        {
            // This is a placeholder for actual mod enable/disable logic
            // In a real implementation, this would:
            // 1. For UE4SS mods: rename folders or modify mods.txt
            // 2. For PAK mods: rename files or move to disabled folder
            // 3. For PalSchema mods: modify configuration files

            await Task.Run(() =>
            {
                _logger.LogDebug($"Applied state {mod.State} to mod {mod.Name}", "ModManager");
            });
        }

        private async Task LoadProfilesAsync()
        {
            try
            {
                var profilesPath = Path.Combine(_palRoot, "ModProfiles");
                if (!Directory.Exists(profilesPath))
                {
                    Directory.CreateDirectory(profilesPath);
                    return;
                }

                var profileFiles = Directory.GetFiles(profilesPath, "*.json");
                foreach (var file in profileFiles)
                {
                    try
                    {
                        var json = await File.ReadAllTextAsync(file);
                        var profile = JsonSerializer.Deserialize<ModProfile>(json);
                        if (profile != null)
                        {
                            _profiles.Add(profile);
                            if (profile.IsActive)
                            {
                                _activeProfile = profile;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Failed to load profile from {file}: {ex.Message}", "ModManager");
                    }
                }

                _logger.LogInfo($"Loaded {_profiles.Count} profiles", "ModManager");
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to load profiles", "ModManager", ex);
            }
        }

        private async Task LoadCollectionsAsync()
        {
            try
            {
                var collectionsPath = Path.Combine(_palRoot, "ModCollections");
                if (!Directory.Exists(collectionsPath))
                {
                    Directory.CreateDirectory(collectionsPath);
                    return;
                }

                var collectionFiles = Directory.GetFiles(collectionsPath, "*.json");
                foreach (var file in collectionFiles)
                {
                    try
                    {
                        var json = await File.ReadAllTextAsync(file);
                        var collection = JsonSerializer.Deserialize<ModCollection>(json);
                        if (collection != null)
                        {
                            _collections.Add(collection);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Failed to load collection from {file}: {ex.Message}", "ModManager");
                    }
                }

                _logger.LogInfo($"Loaded {_collections.Count} collections", "ModManager");
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to load collections", "ModManager", ex);
            }
        }

        private async Task SaveProfileAsync(ModProfile profile)
        {
            try
            {
                var profilesPath = Path.Combine(_palRoot, "ModProfiles");
                Directory.CreateDirectory(profilesPath);

                var filePath = Path.Combine(profilesPath, $"{profile.Id}.json");
                var json = JsonSerializer.Serialize(profile, new JsonSerializerOptions { WriteIndented = true });
                await File.WriteAllTextAsync(filePath, json);

                profile.LastModified = DateTime.Now;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to save profile {profile.Name}", "ModManager", ex);
                throw;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                // Clean up resources
                _disposed = true;
            }
        }
    }
}
