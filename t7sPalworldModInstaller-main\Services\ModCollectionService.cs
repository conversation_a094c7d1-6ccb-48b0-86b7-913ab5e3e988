using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using ModInstallerApp.Models;

namespace ModInstallerApp.Services
{
    /// <summary>
    /// Service for managing mod collection export/import and sharing
    /// </summary>
    public class ModCollectionService : IDisposable
    {
        private readonly string _palRoot;
        private readonly ModManagerService _modManager;
        private readonly PalSchemaProfileManager _palSchemaProfileManager;
        private readonly EnhancedLogger _logger;
        private readonly string _collectionsPath;
        private readonly string _exportsPath;
        private readonly List<ModCollection> _collections = new();
        private bool _disposed = false;

        public event EventHandler<ModCollection>? CollectionCreated;
        public event EventHandler<ModCollection>? CollectionUpdated;
        public event EventHandler<ModCollection>? CollectionDeleted;
        public event EventHandler<ModCollection>? CollectionExported;
        public event EventHandler<ModCollection>? CollectionImported;

        public ModCollectionService(string palRoot, ModManagerService modManager, 
            PalSchemaProfileManager palSchemaProfileManager, EnhancedLogger logger)
        {
            _palRoot = palRoot ?? throw new ArgumentNullException(nameof(palRoot));
            _modManager = modManager ?? throw new ArgumentNullException(nameof(modManager));
            _palSchemaProfileManager = palSchemaProfileManager ?? throw new ArgumentNullException(nameof(palSchemaProfileManager));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            var appDataPath = Path.GetDirectoryName(palRoot) ?? palRoot;
            _collectionsPath = Path.Combine(appDataPath, "ModCollections");
            _exportsPath = Path.Combine(appDataPath, "Exports");

            Directory.CreateDirectory(_collectionsPath);
            Directory.CreateDirectory(_exportsPath);
        }

        /// <summary>
        /// Initializes the service and loads existing collections
        /// </summary>
        public async Task InitializeAsync()
        {
            try
            {
                _logger.LogInfo("Initializing Mod Collection Service", "ModCollection");
                await LoadCollectionsAsync();
                _logger.LogInfo($"Mod Collection Service initialized: {_collections.Count} collections loaded", "ModCollection");
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to initialize Mod Collection Service", "ModCollection", ex);
                throw;
            }
        }

        /// <summary>
        /// Creates a new mod collection from current mod state
        /// </summary>
        public async Task<ModCollection> CreateCollectionAsync(string name, string description = "", bool includeDisabledMods = false)
        {
            try
            {
                _logger.LogInfo($"Creating mod collection: {name}", "ModCollection");

                var collection = new ModCollection
                {
                    Name = name,
                    Description = description,
                    Author = Environment.UserName,
                    CreatedDate = DateTime.Now,
                    ModifiedDate = DateTime.Now
                };

                // Get current mods from mod manager
                var currentMods = await _modManager.GetAllModsAsync();
                
                foreach (var mod in currentMods)
                {
                    if (!includeDisabledMods && !mod.IsEnabled)
                        continue;

                    var collectionItem = new ModCollectionItem
                    {
                        ModId = mod.Id,
                        Name = mod.Name,
                        Version = mod.Version,
                        Author = mod.Author,
                        SourcePath = mod.SourcePath,
                        ArchiveName = mod.ArchiveName,
                        ModType = mod.Type,
                        IsEnabled = mod.IsEnabled,
                        LoadOrder = mod.LoadOrder,
                        Size = mod.Size,
                        LastModified = mod.LastModified,
                        Notes = mod.Description
                    };

                    // Calculate file hash for integrity checking
                    if (File.Exists(mod.SourcePath))
                    {
                        collectionItem.Hash = await CalculateFileHashAsync(mod.SourcePath);
                    }

                    collection.Mods.Add(collectionItem);
                }

                // Set collection metadata
                collection.RequiresUE4SS = collection.Mods.Any(m => m.ModType == ModType.UE4SSMod || m.ModType == ModType.HybridMod);
                collection.RequiresPalSchema = collection.Mods.Any(m => m.ModType == ModType.PalSchemaMod || m.ModType == ModType.HybridMod);
                collection.TotalSize = collection.Mods.Sum(m => m.Size);

                // Save collection
                await SaveCollectionAsync(collection);
                _collections.Add(collection);

                CollectionCreated?.Invoke(this, collection);
                _logger.LogInfo($"Created mod collection: {name} with {collection.Mods.Count} mods", "ModCollection");

                return collection;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to create collection: {name}", "ModCollection", ex);
                throw;
            }
        }

        /// <summary>
        /// Exports a mod collection to a shareable package
        /// </summary>
        public async Task<string> ExportCollectionAsync(string collectionId, string exportPath, 
            bool includeModFiles = true, bool includeConfigurations = true, bool includeProfiles = true)
        {
            try
            {
                var collection = _collections.FirstOrDefault(c => c.Id == collectionId);
                if (collection == null)
                    throw new ArgumentException($"Collection not found: {collectionId}");

                _logger.LogInfo($"Exporting collection: {collection.Name}", "ModCollection");

                var package = new ModCollectionPackage
                {
                    Collection = collection,
                    ExportInfo = new ExportMetadata
                    {
                        IncludeModFiles = includeModFiles,
                        IncludeConfigurations = includeConfigurations,
                        IncludeProfiles = includeProfiles,
                        GameVersion = await GetGameVersionAsync(),
                        UE4SSVersion = await GetUE4SSVersionAsync(),
                        PalSchemaVersion = await GetPalSchemaVersionAsync()
                    }
                };

                // Include mod files if requested
                if (includeModFiles)
                {
                    foreach (var mod in collection.Mods)
                    {
                        if (File.Exists(mod.SourcePath))
                        {
                            var fileData = await File.ReadAllBytesAsync(mod.SourcePath);
                            package.ModArchives[mod.ModId] = fileData;
                        }
                    }
                }

                // Include profiles if requested
                if (includeProfiles)
                {
                    var profiles = _modManager.GetProfiles();
                    package.Profiles.AddRange(profiles);
                }

                // Include PalSchema configurations if requested
                if (includeConfigurations && collection.RequiresPalSchema)
                {
                    var palSchemaProfiles = _palSchemaProfileManager.GetProfiles();
                    foreach (var profile in palSchemaProfiles)
                    {
                        package.PalSchemaConfigs.AddRange(profile.Configurations);
                    }
                }

                // Calculate checksum
                var packageJson = JsonSerializer.Serialize(package, new JsonSerializerOptions { WriteIndented = true });
                package.Checksum = CalculateStringHash(packageJson);

                // Create export file
                var fileName = $"{SanitizeFileName(collection.Name)}_{DateTime.Now:yyyyMMdd_HHmmss}.pmcx";
                var fullExportPath = Path.Combine(exportPath, fileName);

                await using var fileStream = File.Create(fullExportPath);
                await using var zipArchive = new ZipArchive(fileStream, ZipArchiveMode.Create);

                // Add package metadata
                var metadataEntry = zipArchive.CreateEntry("package.json");
                await using var metadataStream = metadataEntry.Open();
                await metadataStream.WriteAsync(Encoding.UTF8.GetBytes(packageJson));

                // Add mod files
                if (includeModFiles)
                {
                    foreach (var mod in collection.Mods)
                    {
                        if (package.ModArchives.ContainsKey(mod.ModId))
                        {
                            var modEntry = zipArchive.CreateEntry($"mods/{mod.ArchiveName}");
                            await using var modStream = modEntry.Open();
                            await modStream.WriteAsync(package.ModArchives[mod.ModId]);
                        }
                    }
                }

                collection.ExportPath = fullExportPath;
                await SaveCollectionAsync(collection);

                CollectionExported?.Invoke(this, collection);
                _logger.LogInfo($"Exported collection: {collection.Name} to {fullExportPath}", "ModCollection");

                return fullExportPath;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to export collection: {collectionId}", "ModCollection", ex);
                throw;
            }
        }

        /// <summary>
        /// Imports a mod collection from a package file
        /// </summary>
        public async Task<ImportResult> ImportCollectionAsync(string packagePath, bool installMods = false, bool applyProfiles = false)
        {
            try
            {
                _logger.LogInfo($"Importing collection from: {packagePath}", "ModCollection");

                if (!File.Exists(packagePath))
                    throw new FileNotFoundException($"Package file not found: {packagePath}");

                ModCollectionPackage package;
                
                // Extract and parse package
                using var fileStream = File.OpenRead(packagePath);
                using var zipArchive = new ZipArchive(fileStream, ZipArchiveMode.Read);

                var metadataEntry = zipArchive.GetEntry("package.json");
                if (metadataEntry == null)
                    throw new InvalidOperationException("Invalid package: missing metadata");

                using var metadataStream = metadataEntry.Open();
                using var reader = new StreamReader(metadataStream);
                var packageJson = await reader.ReadToEndAsync();
                
                package = JsonSerializer.Deserialize<ModCollectionPackage>(packageJson) 
                    ?? throw new InvalidOperationException("Failed to deserialize package");

                // Verify checksum
                var calculatedChecksum = CalculateStringHash(packageJson);
                if (package.Checksum != calculatedChecksum)
                {
                    _logger.LogWarning("Package checksum mismatch - package may be corrupted", "ModCollection");
                }

                // Import collection
                var collection = package.Collection;
                collection.Id = Guid.NewGuid().ToString(); // Generate new ID to avoid conflicts
                collection.InstallCount++;

                // Install mods if requested
                if (installMods)
                {
                    foreach (var mod in collection.Mods)
                    {
                        var modEntry = zipArchive.GetEntry($"mods/{mod.ArchiveName}");
                        if (modEntry != null)
                        {
                            var tempPath = Path.Combine(Path.GetTempPath(), mod.ArchiveName);
                            modEntry.ExtractToFile(tempPath, true);
                            
                            // Install mod using installation engine
                            // This would integrate with the existing installation system
                            _logger.LogInfo($"Installing mod from collection: {mod.Name}", "ModCollection");
                        }
                    }
                }

                // Apply profiles if requested
                if (applyProfiles && package.Profiles.Any())
                {
                    foreach (var profile in package.Profiles)
                    {
                        await _modManager.CreateProfileAsync(profile.Name + "_Imported", profile.Description);
                    }
                }

                // Save imported collection
                await SaveCollectionAsync(collection);
                _collections.Add(collection);

                CollectionImported?.Invoke(this, collection);
                _logger.LogInfo($"Imported collection: {collection.Name} with {collection.Mods.Count} mods", "ModCollection");

                return ImportResult.Success;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to import collection from: {packagePath}", "ModCollection", ex);
                return ImportResult.Failed;
            }
        }

        /// <summary>
        /// Gets all available collections
        /// </summary>
        public IReadOnlyList<ModCollection> GetCollections()
        {
            return _collections.AsReadOnly();
        }

        /// <summary>
        /// Gets a specific collection by ID
        /// </summary>
        public ModCollection? GetCollection(string collectionId)
        {
            return _collections.FirstOrDefault(c => c.Id == collectionId);
        }

        /// <summary>
        /// Deletes a collection
        /// </summary>
        public async Task<bool> DeleteCollectionAsync(string collectionId)
        {
            try
            {
                var collection = _collections.FirstOrDefault(c => c.Id == collectionId);
                if (collection == null)
                    return false;

                var filePath = Path.Combine(_collectionsPath, $"{collection.Id}.json");
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }

                _collections.Remove(collection);
                CollectionDeleted?.Invoke(this, collection);
                
                _logger.LogInfo($"Deleted collection: {collection.Name}", "ModCollection");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to delete collection: {collectionId}", "ModCollection", ex);
                return false;
            }
        }

        private async Task LoadCollectionsAsync()
        {
            _collections.Clear();
            
            if (!Directory.Exists(_collectionsPath))
                return;

            foreach (var file in Directory.GetFiles(_collectionsPath, "*.json"))
            {
                try
                {
                    var json = await File.ReadAllTextAsync(file);
                    var collection = JsonSerializer.Deserialize<ModCollection>(json);
                    if (collection != null)
                    {
                        _collections.Add(collection);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning($"Failed to load collection from {file}: {ex.Message}", "ModCollection");
                }
            }
        }

        private async Task SaveCollectionAsync(ModCollection collection)
        {
            var filePath = Path.Combine(_collectionsPath, $"{collection.Id}.json");
            var json = JsonSerializer.Serialize(collection, new JsonSerializerOptions { WriteIndented = true });
            await File.WriteAllTextAsync(filePath, json);
        }

        private async Task<string> CalculateFileHashAsync(string filePath)
        {
            using var sha256 = SHA256.Create();
            await using var stream = File.OpenRead(filePath);
            var hash = await sha256.ComputeHashAsync(stream);
            return Convert.ToBase64String(hash);
        }

        private string CalculateStringHash(string input)
        {
            using var sha256 = SHA256.Create();
            var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(input));
            return Convert.ToBase64String(hash);
        }

        private string SanitizeFileName(string fileName)
        {
            var invalidChars = Path.GetInvalidFileNameChars();
            return string.Join("_", fileName.Split(invalidChars, StringSplitOptions.RemoveEmptyEntries));
        }

        private async Task<string> GetGameVersionAsync()
        {
            // Implementation would detect game version
            return "Unknown";
        }

        private async Task<string> GetUE4SSVersionAsync()
        {
            // Implementation would detect UE4SS version
            return "Unknown";
        }

        private async Task<string> GetPalSchemaVersionAsync()
        {
            // Implementation would detect PalSchema version
            return "Unknown";
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _disposed = true;
                _logger.LogInfo("Mod Collection Service disposed", "ModCollection");
            }
        }
    }
}
