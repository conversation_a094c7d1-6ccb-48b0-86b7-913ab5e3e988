using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Security.Principal;
using System.Text.Json;
using System.Threading.Tasks;
using ModInstallerApp.Models;

namespace ModInstallerApp.Services
{
    /// <summary>
    /// Service for automated diagnostic report generation and issue detection
    /// </summary>
    public class DiagnosticService : IDisposable
    {
        private readonly string _palRoot;
        private readonly UE4SSDetector _ue4ssDetector;
        private readonly ModManagerService _modManager;
        private readonly EnhancedLogger _logger;
        private readonly string _diagnosticsPath;
        private readonly List<CommonIssue> _commonIssues = new();
        private bool _disposed = false;

        public event EventHandler<DiagnosticReport>? ReportGenerated;
        public event EventHandler<DiagnosticIssue>? IssueDetected;
        public event EventHandler<DiagnosticIssue>? IssueFixed;

        public DiagnosticService(string palRoot, UE4SSDetector ue4ssDetector, 
            ModManagerService modManager, EnhancedLogger logger)
        {
            _palRoot = palRoot ?? throw new ArgumentNullException(nameof(palRoot));
            _ue4ssDetector = ue4ssDetector ?? throw new ArgumentNullException(nameof(ue4ssDetector));
            _modManager = modManager ?? throw new ArgumentNullException(nameof(modManager));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            var appDataPath = Path.GetDirectoryName(palRoot) ?? palRoot;
            _diagnosticsPath = Path.Combine(appDataPath, "Diagnostics");
            Directory.CreateDirectory(_diagnosticsPath);

            InitializeCommonIssues();
        }

        /// <summary>
        /// Generates a comprehensive diagnostic report
        /// </summary>
        public async Task<DiagnosticReport> GenerateReportAsync()
        {
            try
            {
                _logger.LogInfo("Generating diagnostic report", "Diagnostics");

                var report = new DiagnosticReport
                {
                    GeneratedAt = DateTime.Now,
                    SystemInfo = await GatherSystemInformationAsync(),
                    GameInfo = await GatherGameInformationAsync(),
                    ModDiagnostics = await GatherModDiagnosticsAsync(),
                    DetectedIssues = await DetectIssuesAsync(),
                    LogEntries = _logger.GetRecentLogs(TimeSpan.FromHours(24))
                        .Select(l => $"[{l.Timestamp:yyyy-MM-dd HH:mm:ss}] [{l.Level}] [{l.Component}] {l.Message}")
                        .ToList(),
                    PerformanceMetrics = await GatherPerformanceMetricsAsync()
                };

                // Generate recommended actions based on detected issues
                report.RecommendedActions = GenerateRecommendedActions(report.DetectedIssues);

                // Save report
                await SaveReportAsync(report);

                ReportGenerated?.Invoke(this, report);
                _logger.LogInfo($"Diagnostic report generated with {report.DetectedIssues.Count} issues detected", "Diagnostics");

                return report;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to generate diagnostic report", "Diagnostics", ex);
                throw;
            }
        }

        /// <summary>
        /// Detects common issues automatically
        /// </summary>
        public async Task<List<DiagnosticIssue>> DetectIssuesAsync()
        {
            var issues = new List<DiagnosticIssue>();

            try
            {
                _logger.LogInfo("Running automated issue detection", "Diagnostics");

                foreach (var commonIssue in _commonIssues)
                {
                    try
                    {
                        if (commonIssue.DetectionMethod())
                        {
                            var issue = new DiagnosticIssue
                            {
                                Title = commonIssue.Name,
                                Description = commonIssue.Description,
                                Severity = commonIssue.Severity,
                                Category = string.Join(", ", commonIssue.Categories),
                                CanAutoFix = commonIssue.FixMethod != null,
                                AutoFixDescription = commonIssue.CanAutoFix ? $"Auto-fix available for: {commonIssue.Name}" : ""
                            };

                            issues.Add(issue);
                            IssueDetected?.Invoke(this, issue);
                            _logger.LogInfo($"Detected issue: {commonIssue.Name}", "Diagnostics");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Error checking for issue {commonIssue.Name}: {ex.Message}", "Diagnostics");
                    }
                }

                // Additional custom issue detection
                await DetectModConflictsAsync(issues);
                await DetectMissingDependenciesAsync(issues);
                await DetectCorruptedFilesAsync(issues);

                _logger.LogInfo($"Issue detection completed: {issues.Count} issues found", "Diagnostics");
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to detect issues", "Diagnostics", ex);
            }

            return issues;
        }

        /// <summary>
        /// Attempts to automatically fix detected issues
        /// </summary>
        public async Task<bool> AutoFixIssueAsync(string issueId)
        {
            try
            {
                var commonIssue = _commonIssues.FirstOrDefault(i => i.Id == issueId);
                if (commonIssue?.FixMethod == null)
                    return false;

                _logger.LogInfo($"Attempting auto-fix for issue: {commonIssue.Name}", "Diagnostics");

                var success = commonIssue.FixMethod();
                
                if (success)
                {
                    var issue = new DiagnosticIssue
                    {
                        Id = issueId,
                        Title = commonIssue.Name,
                        Description = "Issue has been automatically fixed"
                    };
                    
                    IssueFixed?.Invoke(this, issue);
                    _logger.LogInfo($"Successfully auto-fixed issue: {commonIssue.Name}", "Diagnostics");
                }
                else
                {
                    _logger.LogWarning($"Auto-fix failed for issue: {commonIssue.Name}", "Diagnostics");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error during auto-fix for issue {issueId}", "Diagnostics", ex);
                return false;
            }
        }

        /// <summary>
        /// Gets all available common issues
        /// </summary>
        public IReadOnlyList<CommonIssue> GetCommonIssues()
        {
            return _commonIssues.AsReadOnly();
        }

        private async Task<SystemInformation> GatherSystemInformationAsync()
        {
            var systemInfo = new SystemInformation
            {
                OperatingSystem = Environment.OSVersion.ToString(),
                ProcessorArchitecture = Environment.ProcessorCount + " cores, " + System.Runtime.InteropServices.RuntimeInformation.ProcessArchitecture,
                DotNetVersion = Environment.Version.ToString(),
                UserName = Environment.UserName,
                IsAdministrator = IsRunningAsAdministrator()
            };

            try
            {
                // Get memory information
                var memoryStatus = new MemoryStatus();
                if (GlobalMemoryStatusEx(memoryStatus))
                {
                    systemInfo.TotalMemory = (long)memoryStatus.ullTotalPhys;
                    systemInfo.AvailableMemory = (long)memoryStatus.ullAvailPhys;
                }

                // Get environment variables (filtered for security)
                var safeEnvVars = new[] { "PATH", "TEMP", "TMP", "PROCESSOR_ARCHITECTURE", "NUMBER_OF_PROCESSORS" };
                foreach (var envVar in safeEnvVars)
                {
                    var value = Environment.GetEnvironmentVariable(envVar);
                    if (!string.IsNullOrEmpty(value))
                    {
                        systemInfo.EnvironmentVariables[envVar] = value;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Failed to gather some system information: {ex.Message}", "Diagnostics");
            }

            return systemInfo;
        }

        private async Task<GameInformation> GatherGameInformationAsync()
        {
            var gameInfo = new GameInformation
            {
                GamePath = _palRoot,
                IsGameRunning = IsGameRunning(),
                UE4SSStatus = await _ue4ssDetector.DetectUE4SSAsync(),
                PalSchemaStatus = await _ue4ssDetector.DetectPalSchemaAsync()
            };

            try
            {
                // Get game version
                var gameExePath = Path.Combine(_palRoot, "Palworld.exe");
                if (File.Exists(gameExePath))
                {
                    var versionInfo = FileVersionInfo.GetVersionInfo(gameExePath);
                    gameInfo.GameVersion = versionInfo.FileVersion ?? "Unknown";
                }

                // Get installed mods
                var mods = await _modManager.GetAllModsAsync();
                gameInfo.InstalledMods = mods.Select(m => m.Name).ToList();
                gameInfo.EnabledMods = mods.Where(m => m.IsEnabled).Select(m => m.Name).ToList();

                // Calculate game directory size
                if (Directory.Exists(_palRoot))
                {
                    gameInfo.GameDirectorySize = CalculateDirectorySize(_palRoot);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Failed to gather some game information: {ex.Message}", "Diagnostics");
            }

            return gameInfo;
        }

        private async Task<List<ModDiagnosticInfo>> GatherModDiagnosticsAsync()
        {
            var modDiagnostics = new List<ModDiagnosticInfo>();

            try
            {
                var mods = await _modManager.GetAllModsAsync();
                
                foreach (var mod in mods)
                {
                    var diagnostic = new ModDiagnosticInfo
                    {
                        ModId = mod.Id,
                        Name = mod.Name,
                        Type = mod.Type,
                        IsEnabled = mod.IsEnabled,
                        Size = mod.Size,
                        LastModified = mod.LastModified
                    };

                    // Check mod validity
                    diagnostic.IsValid = File.Exists(mod.SourcePath);
                    
                    if (!diagnostic.IsValid)
                    {
                        diagnostic.Issues.Add("Mod file not found");
                    }

                    // Check for common issues
                    if (mod.Type == ModType.UE4SSMod && !gameInfo.UE4SSStatus.Status == UE4SSInstallStatus.FullyInstalled)
                    {
                        diagnostic.Issues.Add("UE4SS not properly installed");
                    }

                    modDiagnostics.Add(diagnostic);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Failed to gather mod diagnostics: {ex.Message}", "Diagnostics");
            }

            return modDiagnostics;
        }

        private async Task<Dictionary<string, object>> GatherPerformanceMetricsAsync()
        {
            var metrics = new Dictionary<string, object>();

            try
            {
                var performanceMetrics = _logger.GetPerformanceMetrics();
                
                if (performanceMetrics.Any())
                {
                    metrics["AverageInstallTime"] = performanceMetrics
                        .Where(m => m.OperationName.Contains("Install"))
                        .Average(m => m.Duration.TotalSeconds);
                    
                    metrics["TotalOperations"] = performanceMetrics.Count;
                    metrics["SlowestOperation"] = performanceMetrics
                        .OrderByDescending(m => m.Duration)
                        .FirstOrDefault()?.OperationName ?? "None";
                }

                // Add current process metrics
                var currentProcess = Process.GetCurrentProcess();
                metrics["MemoryUsage"] = currentProcess.WorkingSet64;
                metrics["CpuTime"] = currentProcess.TotalProcessorTime.TotalSeconds;
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Failed to gather performance metrics: {ex.Message}", "Diagnostics");
            }

            return metrics;
        }

        private void InitializeCommonIssues()
        {
            _commonIssues.AddRange(new[]
            {
                new CommonIssue
                {
                    Id = "missing_ue4ss",
                    Name = "UE4SS Not Installed",
                    Description = "UE4SS is required for mod functionality but is not properly installed",
                    DetectionMethod = () => _ue4ssDetector.DetectUE4SS().Status != UE4SSInstallStatus.FullyInstalled,
                    Severity = DiagnosticSeverity.Error,
                    Categories = new List<string> { "UE4SS", "Installation" }
                },
                new CommonIssue
                {
                    Id = "game_running",
                    Name = "Game is Running",
                    Description = "Palworld is currently running, which may interfere with mod installation",
                    DetectionMethod = () => IsGameRunning(),
                    Severity = DiagnosticSeverity.Warning,
                    Categories = new List<string> { "Game", "Process" }
                },
                new CommonIssue
                {
                    Id = "insufficient_permissions",
                    Name = "Insufficient Permissions",
                    Description = "The application may not have sufficient permissions to modify game files",
                    DetectionMethod = () => !IsRunningAsAdministrator() && !CanWriteToGameDirectory(),
                    Severity = DiagnosticSeverity.Error,
                    Categories = new List<string> { "Permissions", "Security" }
                }
            });
        }

        private async Task DetectModConflictsAsync(List<DiagnosticIssue> issues)
        {
            // Implementation for detecting mod conflicts
        }

        private async Task DetectMissingDependenciesAsync(List<DiagnosticIssue> issues)
        {
            // Implementation for detecting missing dependencies
        }

        private async Task DetectCorruptedFilesAsync(List<DiagnosticIssue> issues)
        {
            // Implementation for detecting corrupted files
        }

        private List<string> GenerateRecommendedActions(List<DiagnosticIssue> issues)
        {
            var actions = new List<string>();

            foreach (var issue in issues)
            {
                switch (issue.Severity)
                {
                    case DiagnosticSeverity.Critical:
                    case DiagnosticSeverity.Error:
                        actions.Add($"URGENT: {issue.Title} - {issue.Description}");
                        break;
                    case DiagnosticSeverity.Warning:
                        actions.Add($"WARNING: {issue.Title} - Consider addressing this issue");
                        break;
                }
            }

            return actions;
        }

        private async Task SaveReportAsync(DiagnosticReport report)
        {
            var fileName = $"diagnostic_report_{DateTime.Now:yyyyMMdd_HHmmss}.json";
            var filePath = Path.Combine(_diagnosticsPath, fileName);
            
            var json = JsonSerializer.Serialize(report, new JsonSerializerOptions { WriteIndented = true });
            await File.WriteAllTextAsync(filePath, json);
        }

        private bool IsGameRunning()
        {
            return Process.GetProcessesByName("Palworld").Length > 0;
        }

        private bool IsRunningAsAdministrator()
        {
            var identity = WindowsIdentity.GetCurrent();
            var principal = new WindowsPrincipal(identity);
            return principal.IsInRole(WindowsBuiltInRole.Administrator);
        }

        private bool CanWriteToGameDirectory()
        {
            try
            {
                var testFile = Path.Combine(_palRoot, "test_write_permissions.tmp");
                File.WriteAllText(testFile, "test");
                File.Delete(testFile);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private long CalculateDirectorySize(string directoryPath)
        {
            try
            {
                return Directory.GetFiles(directoryPath, "*", SearchOption.AllDirectories)
                    .Sum(file => new FileInfo(file).Length);
            }
            catch
            {
                return 0;
            }
        }

        // P/Invoke for memory status
        [System.Runtime.InteropServices.DllImport("kernel32.dll")]
        private static extern bool GlobalMemoryStatusEx(MemoryStatus lpBuffer);

        private class MemoryStatus
        {
            public uint dwLength = 64;
            public uint dwMemoryLoad;
            public ulong ullTotalPhys;
            public ulong ullAvailPhys;
            public ulong ullTotalPageFile;
            public ulong ullAvailPageFile;
            public ulong ullTotalVirtual;
            public ulong ullAvailVirtual;
            public ulong ullAvailExtendedVirtual;
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _disposed = true;
                _logger.LogInfo("Diagnostic Service disposed", "Diagnostics");
            }
        }
    }
}
